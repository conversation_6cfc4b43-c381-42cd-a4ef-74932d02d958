#include <yaEngineNext/nxt_engine.h>
#include <yaProtoRecord/precord.h>
#include <yaFtypes/fvalue.h>
#include <yaBasicUtils/macro.h>
#include <stddef.h>
#include <string.h>
#include <stdlib.h>
#include <stdbool.h>
#include <arpa/inet.h>

// Include ASN.1 generated headers (will be generated at build time)
#include "asn1_discovery.h"

#define PROTO_NAME     "h225"

// Helper function to extract message type from H225Message
static const char* get_h225_message_type(const H225Message_t *message)
{
    if (!message) {
        return "unknown";
    }

    switch (message->present) {
        case H225Message_PR_setup:
            return "setup";
        case H225Message_PR_callProceeding:
            return "callProceeding";
        case H225Message_PR_connect:
            return "connect";
        case H225Message_PR_alerting:
            return "alerting";
        case H225Message_PR_information:
            return "information";
        case H225Message_PR_releaseComplete:
            return "releaseComplete";
        case H225Message_PR_facility:
            return "facility";
        case H225Message_PR_progress:
            return "progress";
        case H225Message_PR_empty:
            return "empty";
        case H225Message_PR_status:
            return "status";
        case H225Message_PR_statusInquiry:
            return "statusInquiry";
        case H225Message_PR_setupAcknowledge:
            return "setupAcknowledge";
        case H225Message_PR_notify:
            return "notify";
        default:
            return "unknown";
    }
}

// Helper function to extract call identifier
static void extract_call_identifier(precord_t *precord, const CallIdentifier_t *callId)
{
    if (callId && callId->guid.buf && callId->guid.size == 16) {
        precord_put(precord, "call_id", bytes, callId->guid.buf, callId->guid.size);
    }
}

// Helper function to extract conference identifier
static void extract_conference_identifier(precord_t *precord, const ConferenceIdentifier_t *confId)
{
    if (confId && confId->buf && confId->size == 16) {
        precord_put(precord, "conference_id", bytes, confId->buf, confId->size);
    }
}

// Helper function to extract transport address (simplified)
static void extract_transport_address(precord_t *precord, const TransportAddress_t *addr)
{
    if (!addr) {
        return;
    }

    switch (addr->present) {
        case TransportAddress_PR_ipAddress:
            if (addr->choice.ipAddress.ip.buf && addr->choice.ipAddress.ip.size == 4) {
                char ip_str[16];
                snprintf(ip_str, sizeof(ip_str), "%d.%d.%d.%d",
                    addr->choice.ipAddress.ip.buf[0],
                    addr->choice.ipAddress.ip.buf[1],
                    addr->choice.ipAddress.ip.buf[2],
                    addr->choice.ipAddress.ip.buf[3]);
                precord_put(precord, "address", string, ip_str);
                precord_put(precord, "port", uinteger, (uint32_t)addr->choice.ipAddress.port);
            }
            break;
        case TransportAddress_PR_ip6Address:
            if (addr->choice.ip6Address.ip.buf && addr->choice.ip6Address.ip.size == 16) {
                precord_put(precord, "address", bytes, addr->choice.ip6Address.ip.buf, 16);
                precord_put(precord, "port", uinteger, (uint32_t)addr->choice.ip6Address.port);
            }
            break;
        default:
            // Handle other address types if needed
            break;
    }
}

// Helper function to extract alias addresses (simplified)
static void extract_alias_addresses(precord_t *precord, const struct Setup_UUIE__sourceAddress *aliases)
{
    if (!aliases) {
        return;
    }

    precord_put(precord, "alias_count", uinteger, (uint32_t)aliases->list.count);

    // Only extract first alias for simplicity
    if (aliases->list.count > 0 && aliases->list.array[0]) {
        AliasAddress_t *alias = aliases->list.array[0];

        switch (alias->present) {
            case AliasAddress_PR_dialedDigits:
                if (alias->choice.dialedDigits.buf) {
                    precord_put(precord, "alias_type", string, "dialedDigits");
                    precord_put(precord, "alias_value", string, (char*)alias->choice.dialedDigits.buf);
                }
                break;
            case AliasAddress_PR_h323_ID:
                if (alias->choice.h323_ID.buf) {
                    precord_put(precord, "alias_type", string, "h323_ID");
                    precord_put(precord, "alias_value", bytes, alias->choice.h323_ID.buf, alias->choice.h323_ID.size);
                }
                break;
            case AliasAddress_PR_url_ID:
                if (alias->choice.url_ID.buf) {
                    precord_put(precord, "alias_type", string, "url_ID");
                    precord_put(precord, "alias_value", string, (char*)alias->choice.url_ID.buf);
                }
                break;
            case AliasAddress_PR_email_ID:
                if (alias->choice.email_ID.buf) {
                    precord_put(precord, "alias_type", string, "email_ID");
                    precord_put(precord, "alias_value", string, (char*)alias->choice.email_ID.buf);
                }
                break;
            default:
                precord_put(precord, "alias_type", string, "unknown");
                break;
        }
    }
}

// Main dissector function
static int h225_dissect(nxt_engine_t *engine, nxt_session_t *session _U_, nxt_mbuf_t *mbuf)
{
    precord_t *precord = nxt_engine_pktzone_get_precord(engine);
    precord_layer_put_new_layer(precord, PROTO_NAME);

    // Get raw data from mbuf
    const uint8_t *data = nxt_mbuf_get_raw(mbuf, 0);
    size_t data_len = nxt_mbuf_get_length(mbuf);

    // Decode H.225 message using asn1c
    H225Message_t *message = NULL;
    asn_dec_rval_t decode_result;

    decode_result = ber_decode(0, &asn_DEF_H225Message, (void **)&message, data, data_len);
    
    if (decode_result.code != RC_OK) {
        // Decoding failed
        precord_put(precord, "decode_error", string, "H.225 decode failed");
        return 0; // Return 0 to indicate no bytes consumed
    }

    // Extract basic message information
    const char *msg_type = get_h225_message_type(message);
    precord_put(precord, "message_type", string, msg_type);

    // Extract specific fields based on message type
    switch (message->present) {
        case H225Message_PR_setup:
            {
                Setup_UUIE_t *setup = &message->choice.setup;
                
                // Extract call identifier
                extract_call_identifier(precord, &setup->callIdentifier);
                
                // Extract conference identifier
                extract_conference_identifier(precord, &setup->conferenceID);
                
                // Extract H.245 address if present
                if (setup->h245Address) {
                    extract_transport_address(precord, setup->h245Address);
                }

                // Extract source addresses
                if (setup->sourceAddress) {
                    extract_alias_addresses(precord, setup->sourceAddress);
                }

                // Extract destination addresses (simplified)
                if (setup->destinationAddress) {
                    precord_put(precord, "has_dest_address", uinteger, 1);
                }
                
                // Extract boolean flags
                precord_put(precord, "active_mc", uinteger, setup->activeMC ? 1 : 0);
                precord_put(precord, "media_wait_for_connect", uinteger, setup->mediaWaitForConnect ? 1 : 0);
                precord_put(precord, "can_overlap_send", uinteger, setup->canOverlapSend ? 1 : 0);
                
                if (setup->multipleCalls) {
                    precord_put(precord, "multiple_calls", uinteger, setup->multipleCalls ? 1 : 0);
                }

                if (setup->maintainConnection) {
                    precord_put(precord, "maintain_connection", uinteger, setup->maintainConnection ? 1 : 0);
                }
                
                // Extract endpoint identifier if present
                if (setup->endpointIdentifier && setup->endpointIdentifier->buf) {
                    precord_put(precord, "endpoint_id", string, (char*)setup->endpointIdentifier->buf);
                }
            }
            break;
            
        case H225Message_PR_connect:
            {
                Connect_UUIE_t *connect = &message->choice.connect;
                
                extract_call_identifier(precord, &connect->callIdentifier);
                extract_conference_identifier(precord, &connect->conferenceID);
                
                if (connect->h245Address) {
                    extract_transport_address(precord, connect->h245Address);
                }
                
                precord_put(precord, "multiple_calls", uinteger, connect->multipleCalls ? 1 : 0);
                precord_put(precord, "maintain_connection", uinteger, connect->maintainConnection ? 1 : 0);
            }
            break;
            
        case H225Message_PR_releaseComplete:
            {
                ReleaseComplete_UUIE_t *release = &message->choice.releaseComplete;
                
                extract_call_identifier(precord, &release->callIdentifier);
                
                // Extract release reason if present
                if (release->reason) {
                    const char *reason_str = "unknown";
                    switch (release->reason->present) {
                        case ReleaseCompleteReason_PR_noBandwidth:
                            reason_str = "noBandwidth";
                            break;
                        case ReleaseCompleteReason_PR_gatekeeperResources:
                            reason_str = "gatekeeperResources";
                            break;
                        case ReleaseCompleteReason_PR_unreachableDestination:
                            reason_str = "unreachableDestination";
                            break;
                        case ReleaseCompleteReason_PR_destinationRejection:
                            reason_str = "destinationRejection";
                            break;
                        case ReleaseCompleteReason_PR_undefinedReason:
                            reason_str = "undefinedReason";
                            break;
                        default:
                            break;
                    }
                    precord_put(precord, "release_reason", string, reason_str);
                }
            }
            break;
            
        default:
            // For other message types, just extract call identifier if available
            // This would need to be expanded based on the specific message structure
            break;
    }

    // Post the event
    nxt_session_post_event(engine, session, NXT_EVENT_PACKET_MESSAGE, mbuf, precord);

    // Free the decoded message
    ASN_STRUCT_FREE(asn_DEF_H225Message, message);
    
    // Return the number of bytes consumed (entire message)
    return (int)data_len;
}

static int h225_schema_reg(nxt_engine_t *engine _U_, pschema_db_t *db)
{
    /* 注册 schema */
    pschema_t* pschema = pschema_register_proto(db, PRECORD_NOT_IMPORT_TEMPLATE, PROTO_NAME, "H.225 Call Signalling");
    
    // Basic message fields
    pschema_register_field(pschema, "message_type", YA_FT_STRING, "H.225 message type");
    pschema_register_field(pschema, "decode_error", YA_FT_STRING, "Decode error message");
    
    // Call and conference identifiers
    pschema_register_field(pschema, "call_id", YA_FT_BYTES, "Call identifier GUID");
    pschema_register_field(pschema, "conference_id", YA_FT_BYTES, "Conference identifier");
    
    // Address fields
    pschema_register_field(pschema, "h245_address", YA_FT_STRING, "H.245 control channel address");
    pschema_register_field(pschema, "h245_address_port", YA_FT_UINT32, "H.245 control channel port");
    pschema_register_field(pschema, "dest_call_signal_address", YA_FT_STRING, "Destination call signalling address");
    pschema_register_field(pschema, "dest_call_signal_address_port", YA_FT_UINT32, "Destination call signalling port");
    pschema_register_field(pschema, "source_call_signal_address", YA_FT_STRING, "Source call signalling address");
    pschema_register_field(pschema, "source_call_signal_address_port", YA_FT_UINT32, "Source call signalling port");
    
    // Alias addresses
    for (int i = 0; i < 5; i++) {
        char field_name[64];
        char field_desc[128];
        
        snprintf(field_name, sizeof(field_name), "source_alias_%d", i);
        snprintf(field_desc, sizeof(field_desc), "Source alias address %d", i);
        pschema_register_field(pschema, field_name, YA_FT_STRING, field_desc);
        
        snprintf(field_name, sizeof(field_name), "dest_alias_%d", i);
        snprintf(field_desc, sizeof(field_desc), "Destination alias address %d", i);
        pschema_register_field(pschema, field_name, YA_FT_STRING, field_desc);
    }
    
    // Boolean flags
    pschema_register_field(pschema, "active_mc", YA_FT_UINT32, "Active MC flag");
    pschema_register_field(pschema, "media_wait_for_connect", YA_FT_UINT32, "Media wait for connect flag");
    pschema_register_field(pschema, "can_overlap_send", YA_FT_UINT32, "Can overlap send flag");
    pschema_register_field(pschema, "multiple_calls", YA_FT_UINT32, "Multiple calls flag");
    pschema_register_field(pschema, "maintain_connection", YA_FT_UINT32, "Maintain connection flag");
    
    // Other fields
    pschema_register_field(pschema, "endpoint_id", YA_FT_STRING, "Endpoint identifier");
    pschema_register_field(pschema, "release_reason", YA_FT_STRING, "Release complete reason");

    return 0;
}

static nxt_dissector_def_t gDissectorDef =
{
    .name         = "h225",
    .type         = NXT_DISSECTOR_TYPE_APP,
    .schemaRegFun = h225_schema_reg,
    .dissectFun   = h225_dissect,
    .mountAt      = {
      NXT_MNT_NUMBER("tcp", 1719),  // H.225 call signalling port
        NXT_MNT_NUMBER("tcp", 1720),  // H.225 call signalling port
        NXT_MNT_END,
    },
};

NXT_DISSECTOR_INIT(h225)
{
    nxt_dissector_register(&gDissectorDef);
}
