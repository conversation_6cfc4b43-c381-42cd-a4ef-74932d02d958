#include <yaEngineNext/nxt_engine.h>
#include <yaProtoRecord/precord.h>
#include <yaFtypes/fvalue.h>
#include <yaBasicUtils/macro.h>
#include <stddef.h>
#include <string.h>
#include <stdlib.h>
#include <stdbool.h>
#include <arpa/inet.h>

// Include ASN.1 generated headers (will be generated at build time)
#include "asn1_discovery.h"

#define PROTO_NAME     "h323"

// Helper function to extract message type from H323Message
static const char* get_h323_message_type(const H323Message_t *message)
{
    if (!message) {
        return "unknown";
    }

    switch (message->present) {
        case H323Message_PR_gatekeeperRequest:
            return "gatekeeperRequest";
        case H323Message_PR_gatekeeperConfirm:
            return "gatekeeperConfirm";
        case H323Message_PR_gatekeeperReject:
            return "gatekeeperReject";
        case H323Message_PR_registrationRequest:
            return "registrationRequest";
        case H323Message_PR_registrationConfirm:
            return "registrationConfirm";
        case H323Message_PR_registrationReject:
            return "registrationReject";

        default:
            return "unknown";
    }
}

// Helper function to extract transport address (simplified)
static void extract_transport_address(precord_t *precord, const TransportAddress_t *addr)
{
    if (!addr) {
        return;
    }

    switch (addr->present) {
        case TransportAddress_PR_ipAddress:
            if (addr->choice.ipAddress.ip.buf && addr->choice.ipAddress.ip.size == 4) {
                char ip_str[16];
                snprintf(ip_str, sizeof(ip_str), "%d.%d.%d.%d",
                    addr->choice.ipAddress.ip.buf[0],
                    addr->choice.ipAddress.ip.buf[1],
                    addr->choice.ipAddress.ip.buf[2],
                    addr->choice.ipAddress.ip.buf[3]);
                precord_put(precord, "address", string, ip_str);
                precord_put(precord, "port", uinteger, (uint32_t)addr->choice.ipAddress.port);
            }
            break;
        case TransportAddress_PR_ip6Address:
            if (addr->choice.ip6Address.ip.buf && addr->choice.ip6Address.ip.size == 16) {
                precord_put(precord, "address", bytes, addr->choice.ip6Address.ip.buf, 16);
                precord_put(precord, "port", uinteger, (uint32_t)addr->choice.ip6Address.port);
            }
            break;
        default:
            // Handle other address types if needed
            break;
    }
}

// Helper function to extract alias addresses (simplified)
static void extract_alias_addresses(precord_t *precord, const struct RegistrationRequest__endpointAlias *aliases)
{
    if (!aliases) {
        return;
    }

    precord_put(precord, "alias_count", uinteger, (uint32_t)aliases->list.count);

    // Only extract first alias for simplicity
    if (aliases->list.count > 0 && aliases->list.array[0]) {
        AliasAddress_t *alias = aliases->list.array[0];

        switch (alias->present) {
            case AliasAddress_PR_dialedDigits:
                if (alias->choice.dialedDigits.buf) {
                    precord_put(precord, "alias_type", string, "dialedDigits");
                    precord_put(precord, "alias_value", string, (char*)alias->choice.dialedDigits.buf);
                }
                break;
            case AliasAddress_PR_h323_ID:
                if (alias->choice.h323_ID.buf) {
                    precord_put(precord, "alias_type", string, "h323_ID");
                    precord_put(precord, "alias_value", bytes, alias->choice.h323_ID.buf, alias->choice.h323_ID.size);
                }
                break;
            case AliasAddress_PR_url_ID:
                if (alias->choice.url_ID.buf) {
                    precord_put(precord, "alias_type", string, "url_ID");
                    precord_put(precord, "alias_value", string, (char*)alias->choice.url_ID.buf);
                }
                break;
            case AliasAddress_PR_email_ID:
                if (alias->choice.email_ID.buf) {
                    precord_put(precord, "alias_type", string, "email_ID");
                    precord_put(precord, "alias_value", string, (char*)alias->choice.email_ID.buf);
                }
                break;
            default:
                precord_put(precord, "alias_type", string, "unknown");
                break;
        }
    }
}

// Helper function to extract endpoint type information
static void extract_endpoint_type(precord_t *precord, const EndpointType_t *endpointType)
{
    if (!endpointType) {
        return;
    }

    // Extract vendor information if present
    if (endpointType->vendor) {
        VendorIdentifier_t *vendor = endpointType->vendor;
        
        // Extract vendor H.221 non-standard information
        precord_put(precord, "vendor_country_code", uinteger, (uint32_t)vendor->vendor.t35CountryCode);
        precord_put(precord, "vendor_extension", uinteger, (uint32_t)vendor->vendor.t35Extension);
        precord_put(precord, "vendor_manufacturer_code", uinteger, (uint32_t)vendor->vendor.manufacturerCode);
        
        // Extract product ID if present
        if (vendor->productId && vendor->productId->buf) {
            precord_put(precord, "product_id", bytes, vendor->productId->buf, vendor->productId->size);
        }
        
        // Extract version ID if present
        if (vendor->versionId && vendor->versionId->buf) {
            precord_put(precord, "version_id", bytes, vendor->versionId->buf, vendor->versionId->size);
        }
    }

    // Extract endpoint type flags
    precord_put(precord, "is_mc", uinteger, endpointType->mc ? 1 : 0);
    precord_put(precord, "is_undefined_node", uinteger, endpointType->undefinedNode ? 1 : 0);
    
    // Check for specific endpoint types
    if (endpointType->gatekeeper) {
        precord_put(precord, "endpoint_type", string, "gatekeeper");
    } else if (endpointType->gateway) {
        precord_put(precord, "endpoint_type", string, "gateway");
    } else if (endpointType->mcu) {
        precord_put(precord, "endpoint_type", string, "mcu");
    } else if (endpointType->terminal) {
        precord_put(precord, "endpoint_type", string, "terminal");
    } else {
        precord_put(precord, "endpoint_type", string, "unknown");
    }
}

// Helper function to extract reject reasons (simplified)
static const char* get_gatekeeper_reject_reason(const GatekeeperRejectReason_t *reason)
{
    if (!reason) {
        return "unknown";
    }

    switch (reason->present) {
        case GatekeeperRejectReason_PR_resourceUnavailable:
            return "resourceUnavailable";
        case GatekeeperRejectReason_PR_terminalExcluded:
            return "terminalExcluded";
        case GatekeeperRejectReason_PR_invalidRevision:
            return "invalidRevision";
        case GatekeeperRejectReason_PR_undefinedReason:
            return "undefinedReason";
        case GatekeeperRejectReason_PR_securityDenial:
            return "securityDenial";
        default:
            return "unknown";
    }
}

// Helper function to extract registration reject reasons (simplified)
static const char* get_registration_reject_reason(const RegistrationRejectReason_t *reason)
{
    if (!reason) {
        return "unknown";
    }

    switch (reason->present) {
        case RegistrationRejectReason_PR_discoveryRequired:
            return "discoveryRequired";
        case RegistrationRejectReason_PR_invalidRevision:
            return "invalidRevision";
        case RegistrationRejectReason_PR_invalidCallSignalAddress:
            return "invalidCallSignalAddress";
        case RegistrationRejectReason_PR_invalidRASAddress:
            return "invalidRASAddress";
        case RegistrationRejectReason_PR_invalidTerminalType:
            return "invalidTerminalType";
        case RegistrationRejectReason_PR_undefinedReason:
            return "undefinedReason";
        case RegistrationRejectReason_PR_transportNotSupported:
            return "transportNotSupported";
        case RegistrationRejectReason_PR_resourceUnavailable:
            return "resourceUnavailable";
        case RegistrationRejectReason_PR_invalidAlias:
            return "invalidAlias";
        case RegistrationRejectReason_PR_securityDenial:
            return "securityDenial";
        default:
            return "unknown";
    }
}

// Main dissector function
static int h323_dissect(nxt_engine_t *engine, nxt_session_t *session _U_, nxt_mbuf_t *mbuf)
{
    precord_t *precord = nxt_engine_pktzone_get_precord(engine);
    precord_layer_put_new_layer(precord, PROTO_NAME);

    // Get raw data from mbuf
    const uint8_t *data = nxt_mbuf_get_raw(mbuf, 0);
    size_t data_len = nxt_mbuf_get_length(mbuf);

    // Decode H.323 RAS message using asn1c
    H323Message_t *message = NULL;
    asn_dec_rval_t decode_result;

    decode_result = ber_decode(0, &asn_DEF_H323Message, (void **)&message, data, data_len);
    
    if (decode_result.code != RC_OK) {
        // Decoding failed
        precord_put(precord, "decode_error", string, "H.323 decode failed");
        return 0; // Return 0 to indicate no bytes consumed
    }

    // Extract basic message information
    const char *msg_type = get_h323_message_type(message);
    precord_put(precord, "message_type", string, msg_type);

    // Extract specific fields based on message type
    switch (message->present) {
        case H323Message_PR_gatekeeperRequest:
            {
                GatekeeperRequest_t *gkReq = &message->choice.gatekeeperRequest;
                
                // Extract request sequence number
                precord_put(precord, "request_seq_num", uinteger, (uint32_t)gkReq->requestSeqNum);
                
                // Extract RAS address
                extract_transport_address(precord, &gkReq->rasAddress);

                // Extract endpoint type
                extract_endpoint_type(precord, &gkReq->endpointType);

                // Extract gatekeeper identifier if present
                if (gkReq->gatekeeperIdentifier && gkReq->gatekeeperIdentifier->buf) {
                    precord_put(precord, "gatekeeper_id", string, (char*)gkReq->gatekeeperIdentifier->buf);
                }
            }
            break;
            
        case H323Message_PR_gatekeeperConfirm:
            {
                GatekeeperConfirm_t *gkConf = &message->choice.gatekeeperConfirm;
                
                // Extract request sequence number
                precord_put(precord, "request_seq_num", uinteger, (uint32_t)gkConf->requestSeqNum);
                
                // Extract gatekeeper identifier if present
                if (gkConf->gatekeeperIdentifier && gkConf->gatekeeperIdentifier->buf) {
                    precord_put(precord, "gatekeeper_id", string, (char*)gkConf->gatekeeperIdentifier->buf);
                }
                
                // Extract RAS address
                extract_transport_address(precord, &gkConf->rasAddress);
            }
            break;
            
        case H323Message_PR_gatekeeperReject:
            {
                GatekeeperReject_t *gkRej = &message->choice.gatekeeperReject;
                
                // Extract request sequence number
                precord_put(precord, "request_seq_num", uinteger, (uint32_t)gkRej->requestSeqNum);
                
                // Extract gatekeeper identifier if present
                if (gkRej->gatekeeperIdentifier && gkRej->gatekeeperIdentifier->buf) {
                    precord_put(precord, "gatekeeper_id", string, (char*)gkRej->gatekeeperIdentifier->buf);
                }
                
                // Extract reject reason
                const char *reject_reason = get_gatekeeper_reject_reason(&gkRej->rejectReason);
                precord_put(precord, "reject_reason", string, reject_reason);
            }
            break;
            
        case H323Message_PR_registrationRequest:
            {
                RegistrationRequest_t *regReq = &message->choice.registrationRequest;
                
                // Extract request sequence number
                precord_put(precord, "request_seq_num", uinteger, (uint32_t)regReq->requestSeqNum);
                
                // Extract discovery complete flag
                precord_put(precord, "discovery_complete", uinteger, regReq->discoveryComplete ? 1 : 0);
                
                // Extract keep alive flag
                precord_put(precord, "keep_alive", uinteger, regReq->keepAlive ? 1 : 0);
                
                // Extract endpoint type
                extract_endpoint_type(precord, &regReq->endpointType);
                
                // Extract endpoint identifier if present
                if (regReq->endpointIdentifier && regReq->endpointIdentifier->buf) {
                    precord_put(precord, "endpoint_id", string, (char*)regReq->endpointIdentifier->buf);
                }
                
                // Extract gatekeeper identifier if present
                if (regReq->gatekeeperIdentifier && regReq->gatekeeperIdentifier->buf) {
                    precord_put(precord, "gatekeeper_id", string, (char*)regReq->gatekeeperIdentifier->buf);
                }
                
                // Extract endpoint aliases if present
                if (regReq->endpointAlias) {
                    extract_alias_addresses(precord, regReq->endpointAlias);
                }
                
                // Extract boolean flags
                precord_put(precord, "will_supply_uuies", uinteger, regReq->willSupplyUUIEs ? 1 : 0);
                precord_put(precord, "maintain_connection", uinteger, regReq->maintainConnection ? 1 : 0);
            }
            break;
            
        case H323Message_PR_registrationConfirm:
            {
                RegistrationConfirm_t *regConf = &message->choice.registrationConfirm;
                
                // Extract request sequence number
                precord_put(precord, "request_seq_num", uinteger, (uint32_t)regConf->requestSeqNum);
                
                // Extract endpoint identifier
                if (regConf->endpointIdentifier.buf) {
                    precord_put(precord, "endpoint_id", string, (char*)regConf->endpointIdentifier.buf);
                }
                
                // Extract gatekeeper identifier if present
                if (regConf->gatekeeperIdentifier && regConf->gatekeeperIdentifier->buf) {
                    precord_put(precord, "gatekeeper_id", string, (char*)regConf->gatekeeperIdentifier->buf);
                }
                
                // Extract terminal aliases if present (simplified)
                if (regConf->terminalAlias) {
                    precord_put(precord, "has_terminal_alias", uinteger, 1);
                }
                
                // Extract boolean flags
                precord_put(precord, "will_respond_to_irr", uinteger, regConf->willRespondToIRR ? 1 : 0);
                precord_put(precord, "maintain_connection", uinteger, regConf->maintainConnection ? 1 : 0);
            }
            break;
            
        case H323Message_PR_registrationReject:
            {
                RegistrationReject_t *regRej = &message->choice.registrationReject;
                
                // Extract request sequence number
                precord_put(precord, "request_seq_num", uinteger, (uint32_t)regRej->requestSeqNum);
                
                // Extract gatekeeper identifier if present
                if (regRej->gatekeeperIdentifier && regRej->gatekeeperIdentifier->buf) {
                    precord_put(precord, "gatekeeper_id", string, (char*)regRej->gatekeeperIdentifier->buf);
                }
                
                // Extract reject reason
                const char *reject_reason = get_registration_reject_reason(&regRej->rejectReason);
                precord_put(precord, "reject_reason", string, reject_reason);
            }
            break;
            
        default:
            // For other message types, extract common fields
            // This would need to be expanded based on specific message requirements
            break;
    }

    // Post the event
    nxt_session_post_event(engine, session, NXT_EVENT_PACKET_MESSAGE, mbuf, precord);

    // Free the decoded message
    ASN_STRUCT_FREE(asn_DEF_H323Message, message);
    
    // Return the number of bytes consumed (entire message)
    return (int)data_len;
}

static int h323_schema_reg(nxt_engine_t *engine _U_, pschema_db_t *db)
{
    /* 注册 schema */
    pschema_t* pschema = pschema_register_proto(db, PRECORD_NOT_IMPORT_TEMPLATE, PROTO_NAME, "H.323 RAS");
    
    // Basic message fields
    pschema_register_field(pschema, "message_type", YA_FT_STRING, "H.323 RAS message type");
    pschema_register_field(pschema, "decode_error", YA_FT_STRING, "Decode error message");
    pschema_register_field(pschema, "request_seq_num", YA_FT_UINT32, "Request sequence number");
    
    // Address fields
    pschema_register_field(pschema, "ras_address", YA_FT_STRING, "RAS address");
    pschema_register_field(pschema, "ras_address_port", YA_FT_UINT32, "RAS port");
    
    // Identifier fields
    pschema_register_field(pschema, "gatekeeper_id", YA_FT_STRING, "Gatekeeper identifier");
    pschema_register_field(pschema, "endpoint_id", YA_FT_STRING, "Endpoint identifier");
    
    // Vendor information
    pschema_register_field(pschema, "vendor_country_code", YA_FT_UINT32, "Vendor country code");
    pschema_register_field(pschema, "vendor_extension", YA_FT_UINT32, "Vendor extension");
    pschema_register_field(pschema, "vendor_manufacturer_code", YA_FT_UINT32, "Vendor manufacturer code");
    pschema_register_field(pschema, "product_id", YA_FT_BYTES, "Product identifier");
    pschema_register_field(pschema, "version_id", YA_FT_BYTES, "Version identifier");
    
    // Endpoint type information
    pschema_register_field(pschema, "endpoint_type", YA_FT_STRING, "Endpoint type");
    pschema_register_field(pschema, "is_mc", YA_FT_UINT32, "Is multipoint controller");
    pschema_register_field(pschema, "is_undefined_node", YA_FT_UINT32, "Is undefined node");
    
    // Alias addresses
    for (int i = 0; i < 5; i++) {
        char field_name[64];
        char field_desc[128];
        
        snprintf(field_name, sizeof(field_name), "endpoint_alias_%d", i);
        snprintf(field_desc, sizeof(field_desc), "Endpoint alias address %d", i);
        pschema_register_field(pschema, field_name, YA_FT_STRING, field_desc);
        
        snprintf(field_name, sizeof(field_name), "terminal_alias_%d", i);
        snprintf(field_desc, sizeof(field_desc), "Terminal alias address %d", i);
        pschema_register_field(pschema, field_name, YA_FT_STRING, field_desc);
    }
    
    // Boolean flags
    pschema_register_field(pschema, "discovery_complete", YA_FT_UINT32, "Discovery complete flag");
    pschema_register_field(pschema, "keep_alive", YA_FT_UINT32, "Keep alive flag");
    pschema_register_field(pschema, "will_supply_uuies", YA_FT_UINT32, "Will supply UUIEs flag");
    pschema_register_field(pschema, "maintain_connection", YA_FT_UINT32, "Maintain connection flag");
    pschema_register_field(pschema, "will_respond_to_irr", YA_FT_UINT32, "Will respond to IRR flag");
    
    // Time fields
    pschema_register_field(pschema, "time_to_live", YA_FT_UINT32, "Time to live");
    
    // Reason fields
    pschema_register_field(pschema, "reject_reason", YA_FT_STRING, "Reject reason");

    return 0;
}

static nxt_dissector_def_t gDissectorDef =
{
    .name         = "h323",
    .type         = NXT_DISSECTOR_TYPE_APP,
    .schemaRegFun = h323_schema_reg,
    .dissectFun   = h323_dissect,
    .mountAt      = {
        NXT_MNT_NUMBER("udp", 1719),  // H.323 RAS port
        NXT_MNT_END,
    },
};

NXT_DISSECTOR_INIT(h323)
{
    nxt_dissector_register(&gDissectorDef);
}
